{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.13", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"sourceId": 12666125, "sourceType": "datasetVersion", "datasetId": 8004143}], "dockerImageVersionId": 31089, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "!pip install pip3-autoremove\n!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n!pip install unsloth vllm\n!pip install \"huggingface_hub>=0.34.0\" \"datasets>=3.4.1,<4.0.0\"", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "trusted": true, "execution": {"iopub.status.busy": "2025-08-04T08:29:08.881233Z", "iopub.execute_input": "2025-08-04T08:29:08.881495Z", "iopub.status.idle": "2025-08-04T08:35:23.836800Z", "shell.execute_reply.started": "2025-08-04T08:29:08.881473Z", "shell.execute_reply": "2025-08-04T08:35:23.835872Z"}}, "outputs": [{"name": "stdout", "text": "Collecting pip3-autoremove\n  Downloading pip3_autoremove-1.2.2-py2.py3-none-any.whl.metadata (2.2 kB)\nRequirement already satisfied: pip in /usr/local/lib/python3.11/dist-packages (from pip3-autoremove) (24.1.2)\nRequirement already satisfied: setuptools in /usr/local/lib/python3.11/dist-packages (from pip3-autoremove) (75.2.0)\nDownloading pip3_autoremove-1.2.2-py2.py3-none-any.whl (6.7 kB)\nInstalling collected packages: pip3-autoremove\nSuccessfully installed pip3-autoremove-1.2.2\nLooking in indexes: https://download.pytorch.org/whl/cu124\nRequirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\nRequirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (0.21.0+cu124)\nRequirement already satisfied: torchaudio in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\nCollecting xformers\n  Downloading https://download.pytorch.org/whl/cu124/xformers-0.0.29.post3-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (1.0 kB)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch) (3.18.0)\nRequirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch) (4.14.0)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch) (3.5)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch) (3.1.6)\nRequirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch) (2025.5.1)\nCollecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m66.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-cuda-runtime-cu12==12.4.127 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m35.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hCollecting nvidia-cuda-cupti-cu12==12.4.127 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m79.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-cudnn-cu12==9.1.0.70 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cudnn_cu12-9.1.0.70-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-cublas-cu12==12.4.5.8 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cublas_cu12-12.4.5.8-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-cufft-cu12==11.2.1.3 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cufft_cu12-11.2.1.3-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-curand-cu12==10.3.5.147 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_curand_cu12-10.3.5.147-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-cusolver-cu12==11.6.1.9 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cusolver_cu12-11.6.1.9-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hCollecting nvidia-cusparse-cu12==12.3.1.170 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_cusparse_cu12-12.3.1.170-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m8.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch) (0.6.2)\nRequirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch) (2.21.5)\nRequirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\nCollecting nvidia-nvjitlink-cu12==12.4.127 (from torch)\n  Downloading https://download.pytorch.org/whl/cu124/nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m76.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch) (3.2.0)\nRequirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch) (1.13.1)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch) (1.3.0)\nRequirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from torchvision) (1.26.4)\nRequirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from torchvision) (11.2.1)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch) (3.0.2)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (2025.2.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (2022.2.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (2.4.1)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->torchvision) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->torchvision) (2022.2.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->torchvision) (1.4.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->torchvision) (2024.2.0)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->torchvision) (2024.2.0)\nDownloading https://download.pytorch.org/whl/cu124/xformers-0.0.29.post3-cp311-cp311-manylinux_2_28_x86_64.whl (43.4 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.4/43.4 MB\u001b[0m \u001b[31m39.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, xformers\n  Attempting uninstall: nvidia-nvjitlink-cu12\n    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n  Attempting uninstall: nvidia-curand-cu12\n    Found existing installation: nvidia-curand-cu12 10.3.6.82\n    Uninstalling nvidia-curand-cu12-10.3.6.82:\n      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n  Attempting uninstall: nvidia-cufft-cu12\n    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n  Attempting uninstall: nvidia-cuda-runtime-cu12\n    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n  Attempting uninstall: nvidia-cuda-cupti-cu12\n    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n  Attempting uninstall: nvidia-cublas-cu12\n    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n  Attempting uninstall: nvidia-cusparse-cu12\n    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n  Attempting uninstall: nvidia-cudnn-cu12\n    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n  Attempting uninstall: nvidia-cusolver-cu12\n    Found existing installation: nvidia-cusolver-cu12 11.6.3.83\n    Uninstalling nvidia-cusolver-cu12-11.6.3.83:\n      Successfully uninstalled nvidia-cusolver-cu12-11.6.3.83\nSuccessfully installed nvidia-cublas-cu12-12.4.5.8 nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-9.1.0.70 nvidia-cufft-cu12-11.2.1.3 nvidia-curand-cu12-10.3.5.147 nvidia-cusolver-cu12-11.6.1.9 nvidia-cusparse-cu12-12.3.1.170 nvidia-nvjitlink-cu12-12.4.127 xformers-0.0.29.post3\nCollecting unsloth\n  Downloading unsloth-2025.8.1-py3-none-any.whl.metadata (47 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m47.3/47.3 kB\u001b[0m \u001b[31m1.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hCollecting vllm\n  Downloading vllm-0.10.0-cp38-abi3-manylinux1_x86_64.whl.metadata (14 kB)\nCollecting unsloth_zoo>=2025.8.1 (from unsloth)\n  Downloading unsloth_zoo-2025.8.1-py3-none-any.whl.metadata (8.1 kB)\nRequirement already satisfied: torch>=2.4.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (2.6.0+cu124)\nRequirement already satisfied: xformers>=0.0.27.post2 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.0.29.post3)\nCollecting bitsandbytes (from unsloth)\n  Downloading bitsandbytes-0.46.1-py3-none-manylinux_2_24_x86_64.whl.metadata (10 kB)\nRequirement already satisfied: triton>=3.0.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (3.2.0)\nRequirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from unsloth) (25.0)\nCollecting tyro (from unsloth)\n  Downloading tyro-0.9.27-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: transformers!=4.47.0,!=4.52.0,!=4.52.1,!=4.52.2,!=4.52.3,!=4.53.0,>=4.51.3 in /usr/local/lib/python3.11/dist-packages (from unsloth) (4.52.4)\nRequirement already satisfied: datasets<4.0.0,>=3.4.1 in /usr/local/lib/python3.11/dist-packages (from unsloth) (3.6.0)\nRequirement already satisfied: sentencepiece>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.2.0)\nRequirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from unsloth) (4.67.1)\nRequirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from unsloth) (7.0.0)\nRequirement already satisfied: wheel>=0.42.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.45.1)\nRequirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from unsloth) (1.26.4)\nRequirement already satisfied: accelerate>=0.34.1 in /usr/local/lib/python3.11/dist-packages (from unsloth) (1.8.1)\nCollecting trl!=0.15.0,!=0.19.0,!=0.9.0,!=0.9.1,!=0.9.2,!=0.9.3,>=0.7.9 (from unsloth)\n  Downloading trl-0.20.0-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: peft!=0.11.0,>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.15.2)\nRequirement already satisfied: protobuf in /usr/local/lib/python3.11/dist-packages (from unsloth) (3.20.3)\nCollecting huggingface_hub>=0.34.0 (from unsloth)\n  Downloading huggingface_hub-0.34.3-py3-none-any.whl.metadata (14 kB)\nRequirement already satisfied: hf_transfer in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.1.9)\nRequirement already satisfied: diffusers in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.34.0)\nRequirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.21.0+cu124)\nRequirement already satisfied: regex in /usr/local/lib/python3.11/dist-packages (from vllm) (2024.11.6)\nRequirement already satisfied: cachetools in /usr/local/lib/python3.11/dist-packages (from vllm) (5.5.2)\nRequirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (2.32.4)\nCollecting blake3 (from vllm)\n  Downloading blake3-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.2 kB)\nRequirement already satisfied: py-cpuinfo in /usr/local/lib/python3.11/dist-packages (from vllm) (9.0.0)\nCollecting transformers!=4.47.0,!=4.52.0,!=4.52.1,!=4.52.2,!=4.52.3,!=4.53.0,>=4.51.3 (from unsloth)\n  Downloading transformers-4.54.1-py3-none-any.whl.metadata (41 kB)\n\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.7/41.7 kB\u001b[0m \u001b[31m2.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hRequirement already satisfied: tokenizers>=0.21.1 in /usr/local/lib/python3.11/dist-packages (from vllm) (0.21.2)\nRequirement already satisfied: fastapi>=0.115.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (0.115.13)\nRequirement already satisfied: aiohttp in /usr/local/lib/python3.11/dist-packages (from vllm) (3.12.13)\nCollecting openai<=1.90.0,>=1.87.0 (from vllm)\n  Downloading openai-1.90.0-py3-none-any.whl.metadata (26 kB)\nRequirement already satisfied: pydantic>=2.10 in /usr/local/lib/python3.11/dist-packages (from vllm) (2.11.7)\nRequirement already satisfied: prometheus_client>=0.18.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (0.22.1)\nRequirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (from vllm) (11.2.1)\nCollecting prometheus-fastapi-instrumentator>=7.0.0 (from vllm)\n  Downloading prometheus_fastapi_instrumentator-7.1.0-py3-none-any.whl.metadata (13 kB)\nRequirement already satisfied: tiktoken>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (0.9.0)\nCollecting lm-format-enforcer<0.11,>=0.10.11 (from vllm)\n  Downloading lm_format_enforcer-0.10.11-py3-none-any.whl.metadata (17 kB)\nCollecting llguidance<0.8.0,>=0.7.11 (from vllm)\n  Downloading llguidance-0.7.30-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\nCollecting outlines_core==0.2.10 (from vllm)\n  Downloading outlines_core-0.2.10-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.8 kB)\nCollecting diskcache==5.6.3 (from vllm)\n  Downloading diskcache-5.6.3-py3-none-any.whl.metadata (20 kB)\nCollecting lark==1.2.2 (from vllm)\n  Downloading lark-1.2.2-py3-none-any.whl.metadata (1.8 kB)\nCollecting xgrammar==0.1.21 (from vllm)\n  Downloading xgrammar-0.1.21-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.3 kB)\nRequirement already satisfied: typing_extensions>=4.10 in /usr/local/lib/python3.11/dist-packages (from vllm) (4.14.0)\nRequirement already satisfied: filelock>=3.16.1 in /usr/local/lib/python3.11/dist-packages (from vllm) (3.18.0)\nCollecting partial-json-parser (from vllm)\n  Downloading partial_json_parser-0.2.1.1.post6-py3-none-any.whl.metadata (6.1 kB)\nCollecting pyzmq>=25.0.0 (from vllm)\n  Downloading pyzmq-27.0.1-cp311-cp311-manylinux_2_26_x86_64.manylinux_2_28_x86_64.whl.metadata (6.0 kB)\nCollecting msgspec (from vllm)\n  Downloading msgspec-0.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.9 kB)\nCollecting gguf>=0.13.0 (from vllm)\n  Downloading gguf-0.17.1-py3-none-any.whl.metadata (4.3 kB)\nCollecting mistral_common>=1.8.2 (from mistral_common[audio,image]>=1.8.2->vllm)\n  Downloading mistral_common-1.8.3-py3-none-any.whl.metadata (3.8 kB)\nRequirement already satisfied: opencv-python-headless>=4.11.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (4.11.0.86)\nRequirement already satisfied: pyyaml in /usr/local/lib/python3.11/dist-packages (from vllm) (6.0.2)\nRequirement already satisfied: einops in /usr/local/lib/python3.11/dist-packages (from vllm) (0.8.1)\nCollecting compressed-tensors==0.10.2 (from vllm)\n  Downloading compressed_tensors-0.10.2-py3-none-any.whl.metadata (7.0 kB)\nCollecting depyf==0.19.0 (from vllm)\n  Downloading depyf-0.19.0-py3-none-any.whl.metadata (7.3 kB)\nRequirement already satisfied: cloudpickle in /usr/local/lib/python3.11/dist-packages (from vllm) (3.1.1)\nCollecting watchfiles (from vllm)\n  Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\nRequirement already satisfied: python-json-logger in /usr/local/lib/python3.11/dist-packages (from vllm) (3.3.0)\nRequirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from vllm) (1.15.3)\nRequirement already satisfied: ninja in /usr/local/lib/python3.11/dist-packages (from vllm) (********)\nCollecting pybase64 (from vllm)\n  Downloading pybase64-1.4.2-cp311-cp311-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl.metadata (8.7 kB)\nCollecting cbor2 (from vllm)\n  Downloading cbor2-5.6.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.0 kB)\nCollecting numba==0.61.2 (from vllm)\n  Downloading numba-0.61.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.8 kB)\nRequirement already satisfied: ray!=2.44.*,>=2.43.0 in /usr/local/lib/python3.11/dist-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm) (2.47.1)\nCollecting torch>=2.4.0 (from unsloth)\n  Downloading torch-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (29 kB)\nCollecting torchaudio==2.7.1 (from vllm)\n  Downloading torchaudio-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.6 kB)\nCollecting torchvision (from unsloth)\n  Downloading torchvision-0.22.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.1 kB)\nCollecting xformers>=0.0.27.post2 (from unsloth)\n  Downloading xformers-0.0.31-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (1.0 kB)\nCollecting astor (from depyf==0.19.0->vllm)\n  Downloading astor-0.8.1-py2.py3-none-any.whl.metadata (4.2 kB)\nRequirement already satisfied: dill in /usr/local/lib/python3.11/dist-packages (from depyf==0.19.0->vllm) (0.3.8)\nCollecting llvmlite<0.45,>=0.44.0dev0 (from numba==0.61.2->vllm)\n  Downloading llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.8 kB)\nCollecting sympy>=1.13.3 (from torch>=2.4.0->unsloth)\n  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\nRequirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.4.0->unsloth) (3.5)\nRequirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.4.0->unsloth) (3.1.6)\nRequirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=2.4.0->unsloth) (2025.5.1)\nCollecting nvidia-cuda-nvrtc-cu12==12.6.77 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cuda-runtime-cu12==12.6.77 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cuda-cupti-cu12==12.6.80 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cudnn-cu12==9.5.1.17 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cublas-cu12==12.6.4.1 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cufft-cu12==11.3.0.4 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cufft_cu12-11.3.0.4-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-curand-cu12==10.3.7.77 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_curand_cu12-10.3.7.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cusolver-cu12==11.7.1.2 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cusparse-cu12==12.5.4.2 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cusparse_cu12-12.5.4.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-cusparselt-cu12==0.6.3 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl.metadata (6.8 kB)\nCollecting nvidia-nccl-cu12==2.26.2 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.0 kB)\nCollecting nvidia-nvtx-cu12==12.6.77 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\nCollecting nvidia-nvjitlink-cu12==12.6.85 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl.metadata (1.5 kB)\nCollecting nvidia-cufile-cu12==1.11.1.6 (from torch>=2.4.0->unsloth)\n  Downloading nvidia_cufile_cu12-1.11.1.6-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\nCollecting triton>=3.0.0 (from unsloth)\n  Downloading triton-3.3.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (1.5 kB)\nRequirement already satisfied: setuptools>=40.8.0 in /usr/local/lib/python3.11/dist-packages (from triton>=3.0.0->unsloth) (75.2.0)\nRequirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from accelerate>=0.34.1->unsloth) (0.5.3)\nRequirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (19.0.1)\nRequirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (2.2.3)\nRequirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (3.5.0)\nRequirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (0.70.16)\nCollecting fsspec (from torch>=2.4.0->unsloth)\n  Downloading fsspec-2025.3.0-py3-none-any.whl.metadata (11 kB)\nRequirement already satisfied: starlette<0.47.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm) (0.46.2)\nCollecting fastapi-cli>=0.0.5 (from fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading fastapi_cli-0.0.8-py3-none-any.whl.metadata (6.3 kB)\nRequirement already satisfied: httpx>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (0.28.1)\nRequirement already satisfied: python-multipart>=0.0.18 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (0.0.20)\nRequirement already satisfied: email-validator>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (2.2.0)\nRequirement already satisfied: uvicorn>=0.12.0 in /usr/local/lib/python3.11/dist-packages (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (0.34.3)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0->unsloth) (1.1.5)\nCollecting interegular>=0.3.2 (from lm-format-enforcer<0.11,>=0.10.11->vllm)\n  Downloading interegular-0.3.3-py37-none-any.whl.metadata (3.0 kB)\nRequirement already satisfied: jsonschema>=4.21.1 in /usr/local/lib/python3.11/dist-packages (from mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (4.24.0)\nCollecting pydantic-extra-types>=2.10.5 (from pydantic-extra-types[pycountry]>=2.10.5->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm)\n  Downloading pydantic_extra_types-2.10.5-py3-none-any.whl.metadata (3.9 kB)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (2025.2.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (2022.2.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (2.4.1)\nRequirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (4.9.0)\nRequirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (1.9.0)\nRequirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (0.10.0)\nRequirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (1.3.1)\nRequirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.10->vllm) (0.7.0)\nRequirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.10->vllm) (2.33.2)\nRequirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.10->vllm) (0.4.1)\nRequirement already satisfied: click>=7.0 in /usr/local/lib/python3.11/dist-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm) (8.2.1)\nRequirement already satisfied: msgpack<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm) (1.1.1)\nRequirement already satisfied: cupy-cuda12x in /usr/local/lib/python3.11/dist-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm) (13.4.1)\nRequirement already satisfied: charset_normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (3.4.2)\nRequirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (3.10)\nRequirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (2.5.0)\nRequirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (2025.6.15)\nCollecting cut_cross_entropy (from unsloth_zoo>=2025.8.1->unsloth)\n  Downloading cut_cross_entropy-25.1.1-py3-none-any.whl.metadata (9.3 kB)\nRequirement already satisfied: aiohappyeyeballs>=2.5.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (2.6.1)\nRequirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (1.3.2)\nRequirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (25.3.0)\nRequirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (1.7.0)\nRequirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (6.6.3)\nRequirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (0.3.2)\nRequirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (1.20.1)\nRequirement already satisfied: importlib_metadata in /usr/local/lib/python3.11/dist-packages (from diffusers->unsloth) (8.7.0)\nRequirement already satisfied: docstring-parser>=0.15 in /usr/local/lib/python3.11/dist-packages (from tyro->unsloth) (0.16)\nRequirement already satisfied: rich>=11.1.0 in /usr/local/lib/python3.11/dist-packages (from tyro->unsloth) (14.0.0)\nCollecting shtab>=1.5.6 (from tyro->unsloth)\n  Downloading shtab-1.7.2-py3-none-any.whl.metadata (7.4 kB)\nRequirement already satisfied: typeguard>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from tyro->unsloth) (4.4.4)\nRequirement already satisfied: dnspython>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from email-validator>=2.0.0->fastapi[standard]>=0.115.0->vllm) (2.7.0)\nRequirement already satisfied: typer>=0.15.1 in /usr/local/lib/python3.11/dist-packages (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (0.16.0)\nCollecting rich-toolkit>=0.14.8 (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading rich_toolkit-0.14.9-py3-none-any.whl.metadata (999 bytes)\nCollecting fastapi-cloud-cli>=0.1.1 (from fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading fastapi_cloud_cli-0.1.5-py3-none-any.whl.metadata (3.2 kB)\nRequirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm) (1.0.9)\nRequirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm) (0.16.0)\nRequirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.4.0->unsloth) (3.0.2)\nRequirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=4.21.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (2025.4.1)\nRequirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=4.21.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.36.2)\nRequirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=4.21.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.25.1)\nCollecting pycountry>=23 (from pydantic-extra-types[pycountry]>=2.10.5->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm)\n  Downloading pycountry-24.6.1-py3-none-any.whl.metadata (12 kB)\nRequirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=11.1.0->tyro->unsloth) (3.0.0)\nRequirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich>=11.1.0->tyro->unsloth) (2.19.2)\nRequirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy>=1.13.3->torch>=2.4.0->unsloth) (1.3.0)\nCollecting httptools>=0.6.3 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\nCollecting python-dotenv>=0.13 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading python_dotenv-1.1.1-py3-none-any.whl.metadata (24 kB)\nCollecting uvloop>=0.15.1 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\nRequirement already satisfied: websockets>=10.4 in /usr/local/lib/python3.11/dist-packages (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (15.0.1)\nRequirement already satisfied: fastrlock>=0.5 in /usr/local/lib/python3.11/dist-packages (from cupy-cuda12x->ray[cgraph]!=2.44.*,>=2.43.0->vllm) (0.8.3)\nRequirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata->diffusers->unsloth) (3.23.0)\nRequirement already satisfied: soundfile>=0.12.1 in /usr/local/lib/python3.11/dist-packages (from mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.13.1)\nRequirement already satisfied: soxr>=0.5.0 in /usr/local/lib/python3.11/dist-packages (from mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.5.0.post1)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->unsloth) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->unsloth) (2022.2.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->unsloth) (1.4.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->unsloth) (2024.2.0)\nRequirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1->unsloth) (2.9.0.post0)\nRequirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1->unsloth) (2025.2)\nRequirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1->unsloth) (2025.2)\nCollecting rignore>=0.5.1 (from fastapi-cloud-cli>=0.1.1->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\n  Downloading rignore-0.6.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)\nRequirement already satisfied: sentry-sdk>=2.20.0 in /usr/local/lib/python3.11/dist-packages (from fastapi-cloud-cli>=0.1.1->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (2.31.0)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->unsloth) (2024.2.0)\nRequirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=11.1.0->tyro->unsloth) (0.1.2)\nRequirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->datasets<4.0.0,>=3.4.1->unsloth) (1.17.0)\nRequirement already satisfied: cffi>=1.0 in /usr/local/lib/python3.11/dist-packages (from soundfile>=0.12.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (1.17.1)\nRequirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer>=0.15.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (1.5.4)\nRequirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.0->soundfile>=0.12.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (2.22)\nDownloading unsloth-2025.8.1-py3-none-any.whl (299 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m299.8/299.8 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading vllm-0.10.0-cp38-abi3-manylinux1_x86_64.whl (386.6 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m386.6/386.6 MB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading compressed_tensors-0.10.2-py3-none-any.whl (169 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m169.0/169.0 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading depyf-0.19.0-py3-none-any.whl (39 kB)\nDownloading diskcache-5.6.3-py3-none-any.whl (45 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.5/45.5 kB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading lark-1.2.2-py3-none-any.whl (111 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m111.0/111.0 kB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading numba-0.61.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (3.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.8/3.8 MB\u001b[0m \u001b[31m79.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading outlines_core-0.2.10-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m71.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading torch-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl (821.2 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m821.2/821.2 MB\u001b[0m \u001b[31m1.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading torchaudio-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl (3.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.5/3.5 MB\u001b[0m \u001b[31m81.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading torchvision-0.22.1-cp311-cp311-manylinux_2_28_x86_64.whl (7.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.5/7.5 MB\u001b[0m \u001b[31m96.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading triton-3.3.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (155.7 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m155.7/155.7 MB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading xformers-0.0.31-cp39-abi3-manylinux_2_28_x86_64.whl (117.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m117.1/117.1 MB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading xgrammar-0.1.21-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.8/11.8 MB\u001b[0m \u001b[31m98.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m0:01\u001b[0m\n\u001b[?25hDownloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (393.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m393.1/393.1 MB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (8.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m101.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl (23.7 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.7/23.7 MB\u001b[0m \u001b[31m76.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (897 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m897.7/897.7 kB\u001b[0m \u001b[31m27.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl (571.0 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m571.0/571.0 MB\u001b[0m \u001b[31m2.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cufft_cu12-11.3.0.4-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (200.2 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m200.2/200.2 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cufile_cu12-1.11.1.6-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (1.1 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m922.4 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_curand_cu12-10.3.7.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (56.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (158.2 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m158.2/158.2 MB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusparse_cu12-12.5.4.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (216.6 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m216.6/216.6 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl (156.8 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m156.8/156.8 MB\u001b[0m \u001b[31m10.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (201.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.3/201.3 MB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl (19.7 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.7/19.7 MB\u001b[0m \u001b[31m82.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (89 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m89.3/89.3 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading gguf-0.17.1-py3-none-any.whl (96 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m96.2/96.2 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading huggingface_hub-0.34.3-py3-none-any.whl (558 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m558.8/558.8 kB\u001b[0m \u001b[31m23.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading llguidance-0.7.30-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (15.0 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.0/15.0 MB\u001b[0m \u001b[31m88.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading lm_format_enforcer-0.10.11-py3-none-any.whl (44 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.2/44.2 kB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading mistral_common-1.8.3-py3-none-any.whl (6.5 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.5/6.5 MB\u001b[0m \u001b[31m88.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading openai-1.90.0-py3-none-any.whl (734 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m734.6/734.6 kB\u001b[0m \u001b[31m30.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading prometheus_fastapi_instrumentator-7.1.0-py3-none-any.whl (19 kB)\nDownloading pyzmq-27.0.1-cp311-cp311-manylinux_2_26_x86_64.manylinux_2_28_x86_64.whl (856 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m856.6/856.6 kB\u001b[0m \u001b[31m37.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading transformers-4.54.1-py3-none-any.whl (11.2 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.2/11.2 MB\u001b[0m \u001b[31m96.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m0:01\u001b[0m\n\u001b[?25hDownloading trl-0.20.0-py3-none-any.whl (504 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m504.6/504.6 kB\u001b[0m \u001b[31m23.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading unsloth_zoo-2025.8.1-py3-none-any.whl (166 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m166.7/166.7 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading bitsandbytes-0.46.1-py3-none-manylinux_2_24_x86_64.whl (72.9 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.9/72.9 MB\u001b[0m \u001b[31m23.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading blake3-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (385 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m385.5/385.5 kB\u001b[0m \u001b[31m18.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading cbor2-5.6.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (249 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m249.2/249.2 kB\u001b[0m \u001b[31m13.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading msgspec-0.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (210 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m210.7/210.7 kB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading partial_json_parser-0.2.1.1.post6-py3-none-any.whl (10 kB)\nDownloading pybase64-1.4.2-cp311-cp311-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl (71 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.4/71.4 kB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading tyro-0.9.27-py3-none-any.whl (129 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m129.0/129.0 kB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (453 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m453.1/453.1 kB\u001b[0m \u001b[31m20.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading fastapi_cli-0.0.8-py3-none-any.whl (10 kB)\nDownloading fsspec-2025.3.0-py3-none-any.whl (193 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m193.6/193.6 kB\u001b[0m \u001b[31m10.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading interegular-0.3.3-py37-none-any.whl (23 kB)\nDownloading llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (42.4 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.4/42.4 MB\u001b[0m \u001b[31m40.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading pydantic_extra_types-2.10.5-py3-none-any.whl (38 kB)\nDownloading shtab-1.7.2-py3-none-any.whl (14 kB)\nDownloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m70.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m00:01\u001b[0m\n\u001b[?25hDownloading astor-0.8.1-py2.py3-none-any.whl (27 kB)\nDownloading cut_cross_entropy-25.1.1-py3-none-any.whl (22 kB)\nDownloading fastapi_cloud_cli-0.1.5-py3-none-any.whl (18 kB)\nDownloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (459 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m23.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hDownloading pycountry-24.6.1-py3-none-any.whl (6.3 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m97.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)\nDownloading rich_toolkit-0.14.9-py3-none-any.whl (25 kB)\nDownloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.0 MB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m82.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m:00:01\u001b[0m\n\u001b[?25hDownloading rignore-0.6.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (950 kB)\n\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m950.6/950.6 kB\u001b[0m \u001b[31m33.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n\u001b[?25hInstalling collected packages: nvidia-cusparselt-cu12, blake3, uvloop, triton, sympy, shtab, rignore, pyzmq, python-dotenv, pycountry, pybase64, partial-json-parser, outlines_core, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufile-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, msgspec, llvmlite, llguidance, lark, interegular, httptools, fsspec, diskcache, cbor2, astor, watchfiles, nvidia-cusparse-cu12, nvidia-cufft-cu12, nvidia-cudnn-cu12, huggingface_hub, depyf, tyro, rich-toolkit, pydantic-extra-types, prometheus-fastapi-instrumentator, openai, nvidia-cusolver-cu12, lm-format-enforcer, torch, fastapi-cloud-cli, fastapi-cli, torchaudio, cut_cross_entropy, transformers, mistral_common, trl, xgrammar, xformers, unsloth_zoo, torchvision, numba, gguf, compressed-tensors, bitsandbytes, vllm, unsloth\n  Attempting uninstall: nvidia-cusparselt-cu12\n    Found existing installation: nvidia-cusparselt-cu12 0.6.2\n    Uninstalling nvidia-cusparselt-cu12-0.6.2:\n      Successfully uninstalled nvidia-cusparselt-cu12-0.6.2\n  Attempting uninstall: triton\n    Found existing installation: triton 3.2.0\n    Uninstalling triton-3.2.0:\n      Successfully uninstalled triton-3.2.0\n  Attempting uninstall: sympy\n    Found existing installation: sympy 1.13.1\n    Uninstalling sympy-1.13.1:\n      Successfully uninstalled sympy-1.13.1\n  Attempting uninstall: pyzmq\n    Found existing installation: pyzmq 24.0.1\n    Uninstalling pyzmq-24.0.1:\n      Successfully uninstalled pyzmq-24.0.1\n  Attempting uninstall: nvidia-nvtx-cu12\n    Found existing installation: nvidia-nvtx-cu12 12.4.127\n    Uninstalling nvidia-nvtx-cu12-12.4.127:\n      Successfully uninstalled nvidia-nvtx-cu12-12.4.127\n  Attempting uninstall: nvidia-nvjitlink-cu12\n    Found existing installation: nvidia-nvjitlink-cu12 12.4.127\n    Uninstalling nvidia-nvjitlink-cu12-12.4.127:\n      Successfully uninstalled nvidia-nvjitlink-cu12-12.4.127\n  Attempting uninstall: nvidia-nccl-cu12\n    Found existing installation: nvidia-nccl-cu12 2.21.5\n    Uninstalling nvidia-nccl-cu12-2.21.5:\n      Successfully uninstalled nvidia-nccl-cu12-2.21.5\n  Attempting uninstall: nvidia-curand-cu12\n    Found existing installation: nvidia-curand-cu12 10.3.5.147\n    Uninstalling nvidia-curand-cu12-10.3.5.147:\n      Successfully uninstalled nvidia-curand-cu12-10.3.5.147\n  Attempting uninstall: nvidia-cuda-runtime-cu12\n    Found existing installation: nvidia-cuda-runtime-cu12 12.4.127\n    Uninstalling nvidia-cuda-runtime-cu12-12.4.127:\n      Successfully uninstalled nvidia-cuda-runtime-cu12-12.4.127\n  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n    Found existing installation: nvidia-cuda-nvrtc-cu12 12.4.127\n    Uninstalling nvidia-cuda-nvrtc-cu12-12.4.127:\n      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.4.127\n  Attempting uninstall: nvidia-cuda-cupti-cu12\n    Found existing installation: nvidia-cuda-cupti-cu12 12.4.127\n    Uninstalling nvidia-cuda-cupti-cu12-12.4.127:\n      Successfully uninstalled nvidia-cuda-cupti-cu12-12.4.127\n  Attempting uninstall: nvidia-cublas-cu12\n    Found existing installation: nvidia-cublas-cu12 12.4.5.8\n    Uninstalling nvidia-cublas-cu12-12.4.5.8:\n      Successfully uninstalled nvidia-cublas-cu12-12.4.5.8\n  Attempting uninstall: llvmlite\n    Found existing installation: llvmlite 0.43.0\n    Uninstalling llvmlite-0.43.0:\n      Successfully uninstalled llvmlite-0.43.0\n  Attempting uninstall: fsspec\n    Found existing installation: fsspec 2025.5.1\n    Uninstalling fsspec-2025.5.1:\n      Successfully uninstalled fsspec-2025.5.1\n  Attempting uninstall: nvidia-cusparse-cu12\n    Found existing installation: nvidia-cusparse-cu12 12.3.1.170\n    Uninstalling nvidia-cusparse-cu12-12.3.1.170:\n      Successfully uninstalled nvidia-cusparse-cu12-12.3.1.170\n  Attempting uninstall: nvidia-cufft-cu12\n    Found existing installation: nvidia-cufft-cu12 11.2.1.3\n    Uninstalling nvidia-cufft-cu12-11.2.1.3:\n      Successfully uninstalled nvidia-cufft-cu12-11.2.1.3\n  Attempting uninstall: nvidia-cudnn-cu12\n    Found existing installation: nvidia-cudnn-cu12 9.1.0.70\n    Uninstalling nvidia-cudnn-cu12-9.1.0.70:\n      Successfully uninstalled nvidia-cudnn-cu12-9.1.0.70\n  Attempting uninstall: huggingface_hub\n    Found existing installation: huggingface-hub 0.33.1\n    Uninstalling huggingface-hub-0.33.1:\n      Successfully uninstalled huggingface-hub-0.33.1\n  Attempting uninstall: openai\n    Found existing installation: openai 1.91.0\n    Uninstalling openai-1.91.0:\n      Successfully uninstalled openai-1.91.0\n  Attempting uninstall: nvidia-cusolver-cu12\n    Found existing installation: nvidia-cusolver-cu12 11.6.1.9\n    Uninstalling nvidia-cusolver-cu12-11.6.1.9:\n      Successfully uninstalled nvidia-cusolver-cu12-11.6.1.9\n  Attempting uninstall: torch\n    Found existing installation: torch 2.6.0+cu124\n    Uninstalling torch-2.6.0+cu124:\n      Successfully uninstalled torch-2.6.0+cu124\n  Attempting uninstall: torchaudio\n    Found existing installation: torchaudio 2.6.0+cu124\n    Uninstalling torchaudio-2.6.0+cu124:\n      Successfully uninstalled torchaudio-2.6.0+cu124\n  Attempting uninstall: transformers\n    Found existing installation: transformers 4.52.4\n    Uninstalling transformers-4.52.4:\n      Successfully uninstalled transformers-4.52.4\n  Attempting uninstall: xformers\n    Found existing installation: xformers 0.0.29.post3\n    Uninstalling xformers-0.0.29.post3:\n      Successfully uninstalled xformers-0.0.29.post3\n  Attempting uninstall: torchvision\n    Found existing installation: torchvision 0.21.0+cu124\n    Uninstalling torchvision-0.21.0+cu124:\n      Successfully uninstalled torchvision-0.21.0+cu124\n  Attempting uninstall: numba\n    Found existing installation: numba 0.60.0\n    Uninstalling numba-0.60.0:\n      Successfully uninstalled numba-0.60.0\n\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\nbigframes 2.8.0 requires google-cloud-bigquery-storage<3.0.0,>=2.30.0, which is not installed.\ndask-cuda 25.2.0 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\ncuml-cu12 25.2.1 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\ncudf-cu12 25.2.2 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\ndistributed-ucxx-cu12 0.42.0 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\nydata-profiling 4.16.1 requires numba<=0.61,>=0.56.0, but you have numba 0.61.2 which is incompatible.\ncesium 0.12.4 requires numpy<3.0,>=2.0, but you have numpy 1.26.4 which is incompatible.\ngoogle-colab 1.0.0 requires google-auth==2.38.0, but you have google-auth 2.40.3 which is incompatible.\ngoogle-colab 1.0.0 requires notebook==6.5.7, but you have notebook 6.5.4 which is incompatible.\ngoogle-colab 1.0.0 requires pandas==2.2.2, but you have pandas 2.2.3 which is incompatible.\ngoogle-colab 1.0.0 requires requests==2.32.3, but you have requests 2.32.4 which is incompatible.\ngoogle-colab 1.0.0 requires tornado==6.4.2, but you have tornado 6.5.1 which is incompatible.\ngcsfs 2025.3.2 requires fsspec==2025.3.2, but you have fsspec 2025.3.0 which is incompatible.\nfastai 2.7.19 requires torch<2.7,>=1.10, but you have torch 2.7.1 which is incompatible.\nbigframes 2.8.0 requires google-cloud-bigquery[bqstorage,pandas]>=3.31.0, but you have google-cloud-bigquery 3.25.0 which is incompatible.\nbigframes 2.8.0 requires rich<14,>=12.4.4, but you have rich 14.0.0 which is incompatible.\njupyter-kernel-gateway 2.5.2 requires jupyter-client<8.0,>=5.2.0, but you have jupyter-client 8.6.3 which is incompatible.\u001b[0m\u001b[31m\n\u001b[0mSuccessfully installed astor-0.8.1 bitsandbytes-0.46.1 blake3-1.0.5 cbor2-5.6.5 compressed-tensors-0.10.2 cut_cross_entropy-25.1.1 depyf-0.19.0 diskcache-5.6.3 fastapi-cli-0.0.8 fastapi-cloud-cli-0.1.5 fsspec-2025.3.0 gguf-0.17.1 httptools-0.6.4 huggingface_hub-0.34.3 interegular-0.3.3 lark-1.2.2 llguidance-0.7.30 llvmlite-0.44.0 lm-format-enforcer-0.10.11 mistral_common-1.8.3 msgspec-0.19.0 numba-0.61.2 nvidia-cublas-cu12-12.6.4.1 nvidia-cuda-cupti-cu12-12.6.80 nvidia-cuda-nvrtc-cu12-12.6.77 nvidia-cuda-runtime-cu12-12.6.77 nvidia-cudnn-cu12-9.5.1.17 nvidia-cufft-cu12-11.3.0.4 nvidia-cufile-cu12-1.11.1.6 nvidia-curand-cu12-10.3.7.77 nvidia-cusolver-cu12-11.7.1.2 nvidia-cusparse-cu12-12.5.4.2 nvidia-cusparselt-cu12-0.6.3 nvidia-nccl-cu12-2.26.2 nvidia-nvjitlink-cu12-12.6.85 nvidia-nvtx-cu12-12.6.77 openai-1.90.0 outlines_core-0.2.10 partial-json-parser-0.2.1.1.post6 prometheus-fastapi-instrumentator-7.1.0 pybase64-1.4.2 pycountry-24.6.1 pydantic-extra-types-2.10.5 python-dotenv-1.1.1 pyzmq-27.0.1 rich-toolkit-0.14.9 rignore-0.6.4 shtab-1.7.2 sympy-1.14.0 torch-2.7.1 torchaudio-2.7.1 torchvision-0.22.1 transformers-4.54.1 triton-3.3.1 trl-0.20.0 tyro-0.9.27 unsloth-2025.8.1 unsloth_zoo-2025.8.1 uvloop-0.21.0 vllm-0.10.0 watchfiles-1.1.0 xformers-0.0.31 xgrammar-0.1.21\nRequirement already satisfied: huggingface_hub>=0.34.0 in /usr/local/lib/python3.11/dist-packages (0.34.3)\nRequirement already satisfied: datasets<4.0.0,>=3.4.1 in /usr/local/lib/python3.11/dist-packages (3.6.0)\nRequirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (3.18.0)\nRequirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (2025.3.0)\nRequirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (25.0)\nRequirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (6.0.2)\nRequirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (2.32.4)\nRequirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (4.67.1)\nRequirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (4.14.0)\nRequirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (1.1.5)\nRequirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (1.26.4)\nRequirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (19.0.1)\nRequirement already satisfied: dill<0.3.9,>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (0.3.8)\nRequirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (2.2.3)\nRequirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (3.5.0)\nRequirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (0.70.16)\nRequirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /usr/local/lib/python3.11/dist-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (3.12.13)\nRequirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (1.3.8)\nRequirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (1.2.4)\nRequirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (0.1.1)\nRequirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (2025.2.0)\nRequirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (2022.2.0)\nRequirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (2.4.1)\nRequirement already satisfied: charset_normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (3.4.2)\nRequirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (3.10)\nRequirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (2.5.0)\nRequirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (2025.6.15)\nRequirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1) (2.9.0.post0)\nRequirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1) (2025.2)\nRequirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1) (2025.2)\nRequirement already satisfied: aiohappyeyeballs>=2.5.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (2.6.1)\nRequirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (1.3.2)\nRequirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (25.3.0)\nRequirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (1.7.0)\nRequirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (6.6.3)\nRequirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (0.3.2)\nRequirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (1.20.1)\nRequirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->datasets<4.0.0,>=3.4.1) (1.17.0)\nRequirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2024.2.0)\nRequirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2022.2.0)\nRequirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (1.4.0)\nRequirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2024.2.0)\nRequirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2024.2.0)\n", "output_type": "stream"}], "execution_count": 1}, {"cell_type": "code", "source": "import json\nimport torch\nfrom unsloth import FastLanguageModel\nfrom transformers import AutoTokenizer\n\n# ========== Step 1: Load the model ==========\nprint(\"Loading model...\")\nmodel_name = \"unsloth/DeepSeek-R1-0528-Qwen3-8B-unsloth-bnb-4bit\"  # Replace with your preferred model\nmodel, tokenizer = FastLanguageModel.from_pretrained(\n    model_name=model_name,\n    max_seq_length=2048 * 4,\n    dtype=None,\n    load_in_4bit=True,\n)\nmodel = FastLanguageModel.for_inference(model)\n\n# ========== Step 2: Define the scoring function ==========\ndef verify_prompt_json_match(prompt: str, json_str: str) -> float:\n    verification_prompt = f\"\"\"Evaluate if the JSON fulfills the given PROMPT.\n\nPROMPT: {prompt}\nJSON: {json_str}\n\nScore 0-10 for:\n1. Prompt-JSON Match: Does JSON fulfill the prompt?\n\nRespond only with <PERSON>SO<PERSON>:\n{{\"prompt_json_score\": <score>, \"summary\": \"<brief_assessment>\"}}\"\"\"\n    input_text = f\"<|im_start|>user\\n{verification_prompt}<|im_end|>\\n<|im_start|>assistant\\n<think>\\n\"\n\n    inputs = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n\n    output = model.generate(\n        **inputs,\n        max_new_tokens=2048,\n        do_sample=False,\n        pad_token_id=tokenizer.eos_token_id,\n    )\n    \n    decoded_output = tokenizer.decode(output[0], skip_special_tokens=True).strip()\n    try:\n        decoded_output = decoded_output.split(\"</think>\")[1].strip()\n    except:\n        decoded_output = \"\"\n    # Try parsing valid JSON from model output\n    try:\n        return json.loads(decoded_output).get(\"prompt_json_score\", 0.0)\n    except json.JSONDecodeError:\n        if \"```json\" in decoded_output:\n            json_start = decoded_output.find(\"```json\") + 7\n            json_end = decoded_output.find(\"```\", json_start)\n            json_block = decoded_output[json_start:json_end].strip()\n            try:\n                return json.loads(json_block).get(\"prompt_json_score\", 0.0)\n            except json.JSONDecodeError:\n                print(f\"[!] Failed to parse fallback JSON:\\n{json_block}\")\n                return 0.0\n        else:\n            print(f\"[!] Failed to parse model output:\\n{decoded_output}\")\n            return 0.0\n\n# ========== Step 3: Load the dataset ==========\ninput_file = \"/kaggle/input/json-prompt-llm-score/merged_training_dataset.json\"\nprint(f\"Loading dataset from: {input_file}\")\nwith open(input_file, \"r\") as f:\n    dataset = json.load(f)\n\n# ========== Step 4: Score each sample ==========\nprint(\"Scoring dataset...\")\nfor i, item in enumerate(dataset):\n    prompt = item.get(\"prompt\", \"\")\n    generated_json = item.get(\"generated_json\", \"\")\n    score = verify_prompt_json_match(prompt, generated_json)\n    item[\"generated_score\"] = score\n    print(f\"[{i + 1}/{len(dataset)}] Score: {score}\")\n\n# ========== Step 5: Save updated dataset ==========\nsuffix = model_name.split(\"/\")[1]\noutput_file = f\"output_score_{suffix}_think.json\"\nwith open(output_file, \"w\") as f:\n    json.dump(dataset, f, indent=2)\n\nprint(f\"\\nDone! Scored dataset saved to: {output_file}\")\n", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-08-04T08:35:23.838688Z", "iopub.execute_input": "2025-08-04T08:35:23.838989Z", "execution_failed": "2025-08-04T08:45:40.376Z"}}, "outputs": [{"name": "stdout", "text": "🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "output_type": "stream"}, {"name": "stderr", "text": "2025-08-04 08:35:34.541204: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\nWARNING: All log messages before absl::InitializeLog() is called are written to STDERR\nE0000 00:00:1754296534.876579      36 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\nE0000 00:00:1754296534.981527      36 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "output_type": "stream"}, {"name": "stdout", "text": "🦥 Unsloth Zoo will now patch everything to make training faster!\nINFO 08-04 08:36:01 [__init__.py:235] Automatically detected platform cuda.\nLoading model...\n", "output_type": "stream"}, {"name": "stderr", "text": "Unrecognized keys in `rope_scaling` for 'rope_type'='yarn': {'attn_factor'}\n", "output_type": "stream"}, {"name": "stdout", "text": "==((====))==  Unsloth 2025.8.1: Fast Qwen3 patching. Transformers: 4.54.1. vLLM: 0.10.0.\n   \\\\   /|    Tesla T4. Num GPUs = 2. Max memory: 14.741 GB. Platform: Linux.\nO^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 7.5. CUDA Toolkit: 12.6. Triton: 3.3.1\n\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.31. FA2 = False]\n \"-____-\"     Free license: http://github.com/unslothai/unsloth\nUnsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "output_type": "stream"}, {"name": "stderr", "text": "Unrecognized keys in `rope_scaling` for 'rope_type'='yarn': {'attn_factor'}\nUnrecognized keys in `rope_scaling` for 'rope_type'='yarn': {'attn_factor'}\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "model.safetensors.index.json: 0.00B [00:00, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "498e5b5b9ba7420eb5333fce9df4c313"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "model-00001-of-00002.safetensors:   0%|          | 0.00/4.98G [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "99458e316a574b01b43ce460ec5ecdd6"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "model-00002-of-00002.safetensors:   0%|          | 0.00/2.47G [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4221ffa293854abd8369b4ec09298a1f"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a8b78dbba27f47b3abe988e5f9b1da70"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "generation_config.json:   0%|          | 0.00/171 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "67482abfda9c47ba828999fbc782b50a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "tokenizer_config.json: 0.00B [00:00, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1d479f076805406e816b7f05355893c2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "tokenizer.json:   0%|          | 0.00/11.4M [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "be5a43e163cd4a66aa9ff6e8e63ef963"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "special_tokens_map.json:   0%|          | 0.00/472 [00:00<?, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "350d640e48254f37a215be6fe8d6d2da"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "chat_template.jinja: 0.00B [00:00, ?B/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "38bd428c534b4ba8be70b1ea988d9589"}}, "metadata": {}}, {"name": "stdout", "text": "Loading dataset from: /kaggle/input/json-prompt-llm-score/merged_training_dataset.json\nScoring dataset...\n[1/201] Score: 9\n[2/201] Score: 10\n[3/201] Score: 0\n[4/201] Score: 10\n[5/201] Score: 10\n[!] Failed to parse model output:\n\n[6/201] Score: 0.0\n", "output_type": "stream"}], "execution_count": null}, {"cell_type": "code", "source": "", "metadata": {"trusted": true}, "outputs": [], "execution_count": null}]}